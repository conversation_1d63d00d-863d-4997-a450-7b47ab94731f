from flask import Blueprint, request, jsonify
from ..utils import process_api_request, process_manager
from inventory.inventory import Inventory
from kubernetes_inventory.kubernetes import Kubernetes
from migration.buildsheet import buildsheet1
from migration.buildvms import gcloudcmds1
from migration.json_to_excel import json_to_exl
from migration.m2vm import handle_m2vm_request
from migration.move_vali import move_vali
from migration.vmware_excel import vmwarem2vm
from migration.gta_m2vm import gta_m2vm1
from migration.aws_bill import process_aws_bill
from migration.azure_bill import process_azure_bill_gcp
from migration.gcp_aws_scoping import process_gcp_bill_to_aws
from migration.azure_bill_1 import process_azure_bill_aws
from compliance.process import Compliance
from stratozone.stratozone import Stratozone
from glide.init import glide
from inventory.looker_utils import main as looker_main

api_bp = Blueprint('api', __name__)

@api_bp.before_request
def before_request_checks():
    if request.method == 'POST' and "multipart/form-data" in request.content_type:
        pid = request.form.get('uuid')
        if not process_manager.acquire_lock(pid):
            return jsonify(process_manager.get_busy_response()), 409

# A helper to reduce route definition boilerplate
def add_process_route(path, func, name):
    @api_bp.route(path, methods=['POST'], endpoint=name)
    def route_func():
        return process_api_request(func, name)
    # Add the function to the module's globals with a unique name
    globals()[f"{name}_route"] = route_func

# Register all processing routes
routes = [
    ('/looker_url', looker_main, 'looker_url'),
    ('/move_val', move_vali, 'move_val'),
    ('/m2vm', handle_m2vm_request, 'handle_m2vm_request'),
    ('/gta_m2vm', gta_m2vm1, 'gta_m2vm'),
    ('/vmware_m2vm', vmwarem2vm, 'vmware_m2vm'),
    ('/aws_export', process_aws_bill, 'process_aws_bill'),
    ('/azure_export', process_azure_bill_gcp, 'process_azure_bill_gcp'),
    ('/gcp_aws_scoping', process_gcp_bill_to_aws, 'process_gcp_bill_to_aws'),
    ('/azure_export_aws', process_azure_bill_aws, 'process_azure_bill_aws'),
    ('/buildsheet', buildsheet1, 'buildsheet'),
    ('/gcloudcmd', gcloudcmds1, 'gcloudcmd'),
    ('/json_to_xl', json_to_exl, 'json_to_xl'),
    ('/compliance', Compliance, 'compliance'),
    ('/stratozone', Stratozone, 'stratozone'),
    ('/inventory', Inventory, 'inventory'),
    ('/glide', glide, 'glide'),
    ('/kube', Kubernetes, 'kube'),
]
for path, func, name in routes:
    add_process_route(path, func, name)
