<div id="movegroup" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">

			<h3>
				Generate Movergroup Validation Sheet from VM Mapping sheet
				<span class="text" style="color: #009925;"> after migration</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'move_req')"
						id="defaultOpen">Requirements</button>
					<div id="move_req" class="tabcontent">
						<p>
							1. Require <span class="text" style="color:black; font-weight: bold">VM mapping
								sheet</span> filled with move groups assigned. <br>
							2. <span class="text" style="color:black; font-weight: bold">Migration</span>
							and <span class="text" style="color:black; font-weight: bold">VM build
								activity</span> done so that the validation can perform comparison of VM
							mapping sheet data against VMs present in the target cloud (GCP or AWS) for a given movegroup.<br>
							3. For <span class="text" style="color:black; font-weight: bold">AWS</span>:
							Valid AWS ARN with appropriate permissions to access EC2 instances.<br>
							4. For <span class="text" style="color:black; font-weight: bold">GCP</span>:
							Valid Project ID with appropriate permissions to list compute instances.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'move_steps')">Steps</button>
					<div id="move_steps" class="tabcontent">

						<p>1. Select your cloud provider (GCP or AWS).<br>
							2. Enter Movegroup name in the VM mapping sheet to be checked.<br>
							3. For <strong>GCP</strong>: Enter Project ID and ensure all VMs belong to the same project.<br>
							4. For <strong>AWS</strong>: Enter AWS ARN with appropriate permissions and optionally specify regions.<br>
							5. Upload latest copy of VM Mapping sheet.<br>
							6. Click upload button.<br>
							7. An Excel file will be downloaded on your computer after some time.<br>

						</p>

					</div>
					<button class="tablinks" onclick="openCity(event, 'move_out')">Output</button>
					<div id="move_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1LYFBhooF-WVWSRjQ4Te3S_eLtOkMuWT5/view?usp=sharing&ouid=111967750052980188249&rtpof=true&sd=true"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/move_val.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'move_vid')">Video</button>
					<div id="move_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1zgLV5r2PG09IyexNxpZDr_hgFI0kCKy7/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'move_wn')">What Next?</button>
					<div id="move_wn" class="tabcontent">
						<p>
							The output file will show non-compliance of the infrastructure.
							You may want to take corrective action and re-run the tool again.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 600px;">
						<form id="form-ggreen" action="move_val" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'ggreen','');">
							<div class="form-group" style="padding-right:0px;margin-top: 10px;">

								<!-- Provider Selection -->
								<span class="span-form-group">Cloud Provider</span>
								<select class="form-group-class" id="provider" name="provider"
									style="border-radius:5px;" onchange="toggleProviderFields()" required>
									<option value="gcp">Google Cloud Platform (GCP)</option>
									<option value="aws">Amazon Web Services (AWS)</option>
								</select><br><br>

								<span class="span-form-group">Movegroup Name</span>
								<input type="text" placeholder="Enter Movegroup Name"
									class="form-group-class" id="move-group" style="border-radius:5px;"
									name="move-group" pattern="[a-z0-9]+" required><br><br>

								<!-- GCP Fields -->
								<div id="gcp-fields">
									<span class="span-form-group">Project ID</span>
									<input type="text" placeholder="Enter the project id"
										class="form-group-class" id="projid" style="border-radius:5px;"
										name="projid"><br><br>
								</div>

								<!-- AWS Fields -->
								<div id="aws-fields" style="display: none;">
									<span class="span-form-group">AWS ARN</span>
									<input type="text" placeholder="Enter AWS ARN (e.g., arn:aws:iam::123456789012:role/YourRole)"
										class="form-group-class" id="arn" style="border-radius:5px;"
										name="arn"><br><br>
									<span class="span-form-group">Regions (Optional)</span>
									<input type="text" placeholder="Enter regions (comma-separated) or leave blank for common regions"
										class="form-group-class" id="regions" style="border-radius:5px;"
										name="regions"><br><br>
								</div>

								<span class="span-form-group">VM Mapping Excel File</span>
								<input type="file" id="excel-file" style="border-radius:5px;"
									name="excel-file" accept=".xlsx" required><br><br><br>

								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<button id="vall" type="submit"
									class="btn arrow-btn blue-btn"
									style="border-radius:5px;margin-left: -216px;margin-top: -25px;margin-bottom: 15px; background-color: #009925;">Upload</button>
							</div>
						</form>
						<div id="text-block-container-ggreen" style="filter:none"></div>
					</div>
				</div>

				<script>
				function toggleProviderFields() {
					const provider = document.getElementById('provider').value;
					const gcpFields = document.getElementById('gcp-fields');
					const awsFields = document.getElementById('aws-fields');
					const projidInput = document.getElementById('projid');
					const arnInput = document.getElementById('arn');

					if (provider === 'aws') {
						gcpFields.style.display = 'none';
						awsFields.style.display = 'block';
						projidInput.required = false;
						arnInput.required = true;
					} else {
						gcpFields.style.display = 'block';
						awsFields.style.display = 'none';
						projidInput.required = true;
						arnInput.required = false;
					}
				}

				// Initialize on page load
				document.addEventListener('DOMContentLoaded', function() {
					toggleProviderFields();
				});
				</script>
			</div>
		</div>
	</div>
</div>