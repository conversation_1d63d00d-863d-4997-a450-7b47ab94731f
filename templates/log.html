Starting multi-account inventory for 1 accounts
Starting inventory collection for account: ************
Scanning 18 regions: ap-south-2, ap-south-1, eu-north-1...
Using 50 concurrent workers
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in ap-south-2.
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in ap-south-1.
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in eu-north-1.
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in eu-west-3.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:ap-south-2:************:* with an explicit deny in an identity-based policy
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:ap-south-1:************:* with an explicit deny in an identity-based policy
Service iam completed in 9.92 seconds
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in eu-west-2.
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/IAM.csv'
Progress: ************: 1/53 services processed (2%)
Service cf completed in 10.48 seconds
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/CLOUDFRONT.csv'
Progress: ************: 2/53 services processed (4%)
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:eu-north-1:************:* with an explicit deny in an identity-based policy
Service route53 completed in 12.18 seconds
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in eu-west-1.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/ROUTE53.csv'
Progress: ************: 3/53 services processed (6%)
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:eu-west-3:************:* with an explicit deny in an identity-based policy
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in ap-northeast-3.
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in ap-northeast-2.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:eu-west-2:************:* with an explicit deny in an identity-based policy
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in ap-northeast-1.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:eu-west-1:************:* with an explicit deny in an identity-based policy
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:ap-northeast-3:************:* with an explicit deny in an identity-based policy
Service s3 completed in 17.78 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/S3.csv'
Progress: ************: 4/53 services processed (8%)
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in ca-central-1.
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:ap-northeast-2:************:* with an explicit deny in an identity-based policy
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:ap-northeast-1:************:* with an explicit deny in an identity-based policy
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in sa-east-1.
Cloudsearch: An error occurred (NotAuthorized) when calling the DescribeDomains operation: New domain creation not supported on this account. Please reach out to AWS Support for assistance.
Service cloudsearch completed in 18.47 seconds
Skipping CSV generation for 'CLOUDSEARCH': data_list is empty or lacks data rows.
Progress: ************: 5/53 services processed (9%)
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in ap-southeast-1.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:ca-central-1:************:* with an explicit deny in an identity-based policy
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in ap-southeast-2.
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in eu-central-1.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:sa-east-1:************:* with an explicit deny in an identity-based policy
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:ap-southeast-1:************:* with an explicit deny in an identity-based policy
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in us-east-1.
Service api completed in 24.69 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/API_GATEWAY.csv'
Progress: ************: 6/53 services processed (11%)
Service eks completed in 25.26 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/EKS.csv'
Progress: ************: 7/53 services processed (13%)
Service cm completed in 23.86 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/CERTIFICATES.csv'
Progress: ************: 8/53 services processed (15%)
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:ap-southeast-2:************:* with an explicit deny in an identity-based policy
Service ses_stats completed in 24.20 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/SES.csv'
Progress: ************: 9/53 services processed (17%)
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in us-east-2.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:eu-central-1:************:* with an explicit deny in an identity-based policy
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in us-west-1.
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:us-east-1:************:* with an explicit deny in an identity-based policy
Service glue completed in 27.65 seconds
Skipping CSV generation for 'GLUE': data_list is empty or lacks data rows.
Progress: ************: 10/53 services processed (19%)
Service kinesis completed in 26.48 seconds
Skipping CSV generation for 'KINESIS': data_list is empty or lacks data rows.
Progress: ************: 11/53 services processed (21%)
Service glue_db completed in 26.12 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/GLUE_DB.csv'
Progress: ************: 12/53 services processed (23%)
Service dynamo completed in 27.43 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/DYNAMODB.csv'
Progress: ************: 13/53 services processed (25%)
Service storage_gateway completed in 28.12 seconds
Skipping CSV generation for 'STORAGE_GATEWAY': data_list is empty or lacks data rows.
Progress: ************: 14/53 services processed (26%)
Service sm completed in 27.56 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/SECRETS.csv'
Progress: ************: 15/53 services processed (28%)
Service firehose completed in 26.87 seconds
Skipping CSV generation for 'FIREHOSE': data_list is empty or lacks data rows.
Progress: ************: 16/53 services processed (30%)
Service efs completed in 28.20 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/EFS.csv'
Progress: ************: 17/53 services processed (32%)
Service appsync completed in 26.93 seconds
Skipping CSV generation for 'APPSYNC': data_list is empty or lacks data rows.
Progress: ************: 18/53 services processed (34%)
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:us-east-2:************:* with an explicit deny in an identity-based policy
Service rds completed in 29.29 seconds
Skipping CSV generation for 'RDS': data_list is empty or lacks data rows.
Progress: ************: 19/53 services processed (36%)
Service opensearch completed in 29.15 seconds
Service mwaa completed in 29.77 seconds
interconnect: An error occurred (DirectConnectClientException) when calling the DescribeInterconnects operation: Account ************ is not an authorized Direct Connect partner in us-west-2.
Skipping CSV generation for 'OPENSEARCH': data_list is empty or lacks data rows.
Progress: ************: 20/53 services processed (38%)
Skipping CSV generation for 'MWAA': data_list is empty or lacks data rows.
Progress: ************: 21/53 services processed (40%)
Service interconnect completed in 29.42 seconds
Service docdb completed in 29.72 seconds
Skipping CSV generation for 'INTERCONNECT': data_list is empty or lacks data rows.
Progress: ************: 22/53 services processed (42%)
Skipping CSV generation for 'DOCDB_CLUSTER': data_list is empty or lacks data rows.
Progress: ************: 23/53 services processed (43%)
Service wafv2 completed in 29.39 seconds
Skipping CSV generation for 'WAF_V2': data_list is empty or lacks data rows.
Progress: ************: 24/53 services processed (45%)
Service neptune completed in 30.03 seconds
Skipping CSV generation for 'NEPTUNE_CLUSTER': data_list is empty or lacks data rows.
Progress: ************: 25/53 services processed (47%)
Service docdb_instance completed in 30.56 seconds
Skipping CSV generation for 'DOCDB_INSTANCE': data_list is empty or lacks data rows.
Progress: ************: 26/53 services processed (49%)
Service cognito completed in 29.57 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/COGNITO.csv'
Progress: ************: 27/53 services processed (51%)
Service transfer_server completed in 18.34 seconds
Skipping CSV generation for 'TRANSFER_SERVER': data_list is empty or lacks data rows.
Service transfer_connector completed in 20.03 seconds
Progress: ************: 28/53 services processed (53%)
Skipping CSV generation for 'TRANSFER_CONNECTOR': data_list is empty or lacks data rows.
Progress: ************: 29/53 services processed (55%)
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:us-west-1:************:* with an explicit deny in an identity-based policy
Service sqs completed in 30.14 seconds
Skipping CSV generation for 'SQS': data_list is empty or lacks data rows.
Progress: ************: 30/53 services processed (57%)
Service neptune_instance completed in 30.80 seconds
Skipping CSV generation for 'NEPTUNE_INSTANCE': data_list is empty or lacks data rows.
Progress: ************: 31/53 services processed (58%)
Service backup completed in 21.10 seconds
Skipping CSV generation for 'BACKUP': data_list is empty or lacks data rows.
Progress: ************: 32/53 services processed (60%)
Service ecs completed in 31.83 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/ECS.csv'
Skipping CSV generation for 'ECS_SERVICE': data_list is empty or lacks data rows.
Progress: ************: 33/53 services processed (62%)
Service ecache completed in 31.22 seconds
Skipping CSV generation for 'ELASTICACHE_CLUSTER': data_list is empty or lacks data rows.
Progress: ************: 34/53 services processed (64%)
Service ecachesl completed in 31.17 seconds
Skipping CSV generation for 'ELASTICACHE_SERVER': data_list is empty or lacks data rows.
Progress: ************: 35/53 services processed (66%)
Service sfn completed in 30.19 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/SFN.csv'
Progress: ************: 36/53 services processed (68%)
Service eventbridge completed in 30.51 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/EVENTBRIDGE.csv'
Progress: ************: 37/53 services processed (70%)
Service ds completed in 30.63 seconds
Skipping CSV generation for 'DIRECTORY': data_list is empty or lacks data rows.
Progress: ************: 38/53 services processed (72%)
Service aws_lambda completed in 32.04 seconds
Protecting original column 'Name', tag value will be ignored.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/LAMBDA.csv'
Progress: ************: 39/53 services processed (74%)
PMS: An error occurred (AccessDeniedException) when calling the DescribeParameters operation: User: arn:aws:sts::************:assumed-role/SearceHydraEKSReadOnlyRoleInventory/google-identity-session is not authorized to perform: ssm:DescribeParameters on resource: arn:aws:ssm:us-west-2:************:* with an explicit deny in an identity-based policy
Service pms completed in 30.39 seconds
Skipping CSV generation for 'PMS': data_list is empty or lacks data rows.
Progress: ************: 40/53 services processed (75%)
Service red completed in 31.68 seconds
Skipping CSV generation for 'REDSHIFT': data_list is empty or lacks data rows.
Progress: ************: 41/53 services processed (77%)
Service emr completed in 32.32 seconds
Skipping CSV generation for 'EMR': data_list is empty or lacks data rows.
Progress: ************: 42/53 services processed (79%)
Service asgs completed in 30.57 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/AUTOSCALING.csv'
Progress: ************: 43/53 services processed (81%)
Service transit_gateway completed in 32.06 seconds
Skipping CSV generation for 'TRANSIT_GATEWAY': data_list is empty or lacks data rows.
Progress: ************: 44/53 services processed (83%)
Service elbv1 completed in 32.32 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/ELBV1.csv'
Progress: ************: 45/53 services processed (85%)
Service elbv2 completed in 32.52 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/ELBV2.csv'
Progress: ************: 46/53 services processed (87%)
Service ecr completed in 32.07 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/ECR.csv'
Progress: ************: 47/53 services processed (89%)
Service ec2 completed in 34.97 seconds
Protecting original column 'Name', tag value will be ignored.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/EC2.csv'
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/EBS.csv'
Progress: ************: 48/53 services processed (91%)
Service vpn completed in 34.75 seconds
Skipping CSV generation for 'VPN': data_list is empty or lacks data rows.
Progress: ************: 49/53 services processed (92%)
Service amplify completed in 33.69 seconds
Skipping CSV generation for 'AMPLIFY': data_list is empty or lacks data rows.
Progress: ************: 50/53 services processed (94%)
Service sns completed in 34.04 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/SNS.csv'
Progress: ************: 51/53 services processed (96%)
Service vpc completed in 39.05 seconds
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/VPC.csv'
Skipping CSV generation for 'SUBNET': data_list is empty or lacks data rows.
DataFrame does not contain a 'Tags' column. Returning original DataFrame.
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/ROUTES.csv'
Progress: ************: 52/53 services processed (98%)
Service kms completed in 45.71 seconds
Successfully saved DataFrame to '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/csv_066b2cd6-364f-49b6-adb2-52f8f1c10590/KMS.csv'
Progress: ************: 53/53 services processed (100%)
Converting CSV files to Excel output: /home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/output/************.xlsx
Ensured output directory exists: '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/output'
Attempting to create Excel file: '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/output/************.xlsx'
Successfully added sheet 'API_GATEWAY' from file 'API_GATEWAY.csv'.
Successfully added sheet 'AUTOSCALING' from file 'AUTOSCALING.csv'.
Successfully added sheet 'CERTIFICATES' from file 'CERTIFICATES.csv'.
Successfully added sheet 'CLOUDFRONT' from file 'CLOUDFRONT.csv'.
Successfully added sheet 'COGNITO' from file 'COGNITO.csv'.
Successfully added sheet 'DYNAMODB' from file 'DYNAMODB.csv'.
Successfully added sheet 'EBS' from file 'EBS.csv'.
Successfully added sheet 'EC2' from file 'EC2.csv'.
Successfully added sheet 'ECR' from file 'ECR.csv'.
Successfully added sheet 'ECS' from file 'ECS.csv'.
Successfully added sheet 'EFS' from file 'EFS.csv'.
Successfully added sheet 'EKS' from file 'EKS.csv'.
Successfully added sheet 'ELBV1' from file 'ELBV1.csv'.
Successfully added sheet 'ELBV2' from file 'ELBV2.csv'.
Successfully added sheet 'EVENTBRIDGE' from file 'EVENTBRIDGE.csv'.
Successfully added sheet 'GLUE_DB' from file 'GLUE_DB.csv'.
Successfully added sheet 'IAM' from file 'IAM.csv'.
Successfully added sheet 'KMS' from file 'KMS.csv'.
Successfully added sheet 'LAMBDA' from file 'LAMBDA.csv'.
Successfully added sheet 'ROUTE53' from file 'ROUTE53.csv'.
Successfully added sheet 'ROUTES' from file 'ROUTES.csv'.
Successfully added sheet 'S3' from file 'S3.csv'.
Successfully added sheet 'SECRETS' from file 'SECRETS.csv'.
Successfully added sheet 'SES' from file 'SES.csv'.
Successfully added sheet 'SFN' from file 'SFN.csv'.
Successfully added sheet 'SNS' from file 'SNS.csv'.
Successfully added sheet 'VPC' from file 'VPC.csv'.
Successfully created Excel file '/home/<USER>/ce-ps3-product-repo/inventory/aws_inventory/output/************.xlsx' with 27 sheets.
Completed account: ************ in 49.83 seconds
Account ************ processing succeeded
Multi-account inventory completed in 49.90 seconds
Processed 1 accounts: 1 succeeded, 0 failed
