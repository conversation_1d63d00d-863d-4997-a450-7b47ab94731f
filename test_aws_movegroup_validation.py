#!/usr/bin/env python3
"""
Test script for AWS movegroup validation functionality
"""

import sys
import os
import json
import pandas as pd
from unittest.mock import Mock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_aws_validation_function():
    """Test the AWS validation function with mock data"""
    
    # Import the function
    from migration.move_vali import aws_mov_validate
    
    # Create mock EC2 data (similar to what the ec2 function would return)
    mock_ec2_data = {
        'EC2': [
            # Header row
            ['Region', 'Name', 'Instance Id', 'Instance Type', 'CPU Cores', 'Memory', 
             'Availability Zone', 'Private Ip', 'Public Ip', 'CreatedAt', 'State', 
             'Subnet Id', 'Vpc Id', 'Platform', 'PlatformDetails', 'Security Groups', 
             'Volumes', 'VolumeId', 'size', 'Total Size', 'Type', 'Iops', 'Snapshot', 'Tags'],
            # Sample instance data
            ['us-east-1', 'test-instance-1', 'i-1234567890abcdef0', 't3.medium', '2', '4', 
             'us-east-1a', '**********', '************', '2024-01-01 10:00:00', 'running', 
             'subnet-12345', 'vpc-12345', 'Linux/UNIX', 'Linux/UNIX (Amazon VPC)', 'sg-12345', 
             'vol-12345', 'vol-12345', '20', '20', 'gp3', '3000', 'snap-12345', 'Environment:test;Project:migration'],
            ['us-east-1', 'test-instance-2', 'i-0987654321fedcba0', 't3.large', '2', '8', 
             'us-east-1b', '**********', '', '2024-01-01 11:00:00', 'running', 
             'subnet-67890', 'vpc-12345', 'Linux/UNIX', 'Linux/UNIX (Amazon VPC)', 'sg-67890', 
             'vol-67890', 'vol-67890', '30', '30', 'gp3', '3000', 'snap-67890', 'Environment:prod;Project:migration']
        ]
    }
    
    # Create a mock VM mapping Excel file
    test_dir = '/tmp/test_aws_validation'
    os.makedirs(test_dir, exist_ok=True)
    
    # Create test VM mapping data
    vm_mapping_data = {
        'Movegroup': ['mg1', 'mg1', 'mg2'],
        'Instance Name': ['test-instance-1', 'test-instance-2', 'test-instance-3'],
        'Name': ['Test VM 1', 'Test VM 2', 'Test VM 3'],
        'Migration Path': ['Lift and Shift', 'Lift and Shift', 'Replatform'],
        'Target Size': ['t3.medium', 't3.large', 't3.small'],
        'Target Disk Type': ['gp3', 'gp3', 'gp2'],
        'Target Internal IP': ['**********', '**********', '**********'],
        'Need for External IP': ['Yes', 'No', 'Yes'],
        'External IP': ['ephemeral', '', '************'],
        'Tags': ['Environment:test', 'Environment:prod', 'Environment:dev']
    }
    
    df = pd.DataFrame(vm_mapping_data)
    excel_path = os.path.join(test_dir, 'test_vm_mapping.xlsx')
    df.to_excel(excel_path, index=False)
    
    # Test the AWS validation function
    test_arn = 'arn:aws:iam::123456789012:role/TestRole'
    
    try:
        result_file, error = aws_mov_validate('mg1', mock_ec2_data, excel_path, test_dir, test_arn)
        
        if error:
            print(f"❌ Test failed with error: {error}")
            return False
        
        if os.path.exists(result_file):
            print(f"✅ Test passed! Validation file created: {result_file}")
            
            # Read and display the results
            result_df = pd.read_excel(result_file)
            print("\n📊 Validation Results:")
            print(result_df.to_string(index=False))
            
            # Clean up
            os.remove(result_file)
            os.remove(excel_path)
            os.rmdir(test_dir)
            
            return True
        else:
            print(f"❌ Test failed: Result file not created")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_provider_selection():
    """Test that the provider selection logic works correctly"""
    
    # Mock request object for GCP
    class MockRequestGCP:
        def __init__(self):
            self.form = {
                'move-group': 'test-mg',
                'provider': 'gcp',
                'projid': 'test-project-123'
            }
            self.files = {'excel-file': Mock()}
            self.files['excel-file'].filename = 'test.xlsx'
            self.files['excel-file'].save = Mock()
    
    # Mock request object for AWS
    class MockRequestAWS:
        def __init__(self):
            self.form = {
                'move-group': 'test-mg',
                'provider': 'aws',
                'arn': 'arn:aws:iam::123456789012:role/TestRole',
                'regions': 'us-east-1,us-west-2'
            }
            self.files = {'excel-file': Mock()}
            self.files['excel-file'].filename = 'test.xlsx'
            self.files['excel-file'].save = Mock()
    
    print("🧪 Testing provider selection logic...")
    
    # Test GCP path
    gcp_request = MockRequestGCP()
    provider = gcp_request.form.get('provider', 'gcp')
    assert provider == 'gcp', f"Expected 'gcp', got '{provider}'"
    print("✅ GCP provider selection works")
    
    # Test AWS path
    aws_request = MockRequestAWS()
    provider = aws_request.form.get('provider', 'gcp')
    assert provider == 'aws', f"Expected 'aws', got '{provider}'"
    print("✅ AWS provider selection works")
    
    return True

def main():
    """Run all tests"""
    print("🚀 Starting AWS Movegroup Validation Tests\n")
    
    # Test 1: Provider selection logic
    if not test_provider_selection():
        print("❌ Provider selection test failed")
        return 1
    
    print()
    
    # Test 2: AWS validation function
    if not test_aws_validation_function():
        print("❌ AWS validation function test failed")
        return 1
    
    print("\n🎉 All tests passed!")
    return 0

if __name__ == '__main__':
    sys.exit(main())
