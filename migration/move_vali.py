import pandas as pd
import os
import re
from os import path
import subprocess
import json
import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON>ill
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter
from inventory.aws_inventory.services.ec2 import ec2
from inventory.aws_inventory.utilities.awsconfig import assume_aws_role_with_google_id_token

def vm_list_func(s):
    # Split the string into lines
    lines = s.strip().split('\n') #here strip removes trailing and leading zeros. Then split, splits the string on new line.

    # Extract the names from each line, excluding the header line
    names = [line.split()[0] for line in lines[1:]] #here the line is split on whitespaces and the 0th is the name.

    # Filter out empty names and strip any whitespace
    A = [name.strip() for name in names if name.strip() != '']

    return A

def extractor(i,s):
    split_parts = s.split('/')
    # Get the last part after splitting
    res = split_parts[-i] #Machine Type
    return res


#FLASK_PART_START
#Configure

#FLASK_PART_START

def mov_validate(mgg, data, path, dir, pid):

    xls = pd.ExcelFile(path)
    oname = 'validate_'+mgg+'_'+pid+'.xlsx'
    result_xl = os.path.join(dir,oname)

    json_data = json.loads(data)
    instances_list = list()

    for instance in json_data:
        instances_list.append(instance['name'])

    print(instances_list)

    #Create a Data Frame
    mg = mgg
    data = pd.DataFrame({"VM Name":[],
                        "Status":[],
                        "Move Group":[],
                        "Treatment":[],
                        "Target Size":[],
                        "Count of Disks":[],
                        "Target Disk Type":[],
                        "Project":[],
                        "Region":[],
                        "Zone":[],
                        "VPC":[],
                        "Subnet":[],
                        "Internal IP":[],
                        "External IP":[],
                        "Network Tags":[],
                        "Service Account":[],
                        "Labels":[]})


    sheets = xls.sheet_names

    df = pd.read_excel(xls, sheets[0]) #Reading the VMs sheet in VMMapping

    df.fillna("", inplace=True)


    for index, row in df.iterrows():
        try:
            print(row['Movegroup'])
        except:
            return '',"Please check whether you have uploaded the proper excel file."
        if row['Movegroup'] == mg:
            MapName = row['Instance Name']

            if MapName.lower() in instances_list:

                name = row['Name'] #VM Name

                #Network Tags in Array Form
                nettags = []
                tags = row['Network Tag'].strip().split('\n')
                for tag in tags:
                    nettags.append(tag)
                nettags.sort()
                #End of Nettags


                #option for externalIP

                if(row['Need for External IP']=='Yes' and row['External IP']==""):
                    print('in if')
                    exipopt = 'ephemeral'
                elif(row['Need for External IP']=='Yes' and row['External IP']!=""):
                    print('in elif')
                    exipopt = row['External IP']
                else:
                    exipopt = 'No External IP'


                print(exipopt)
                i = instances_list.index(name)

                json_machine_type = extractor(1, json_data[i]['machineType'])
                json_project = extractor(5, json_data[i]['selfLink'])
                json_zone = extractor(3, json_data[i]['selfLink'])
                json_region = json_zone[:-2]
                json_vpc = extractor(1, json_data[i]['networkInterfaces'][0]['network'])
                json_subnet = extractor(1, json_data[i]['networkInterfaces'][0]['subnetwork'])

                json_disktype = json_data[i]['disks'][0]['type'].lower()

                if(json_disktype == 'persistent'):
                    json_disktype = 'balanced'



                try:
                    json_nettags = json_data[i]['tags']['items']
                    json_nettags.sort()
                except KeyError:
                    json_nettags = 'No Net Tag'

                try:
                    json_labels = json_data[i]['labels']
                except KeyError:
                    json_labels = 'No Labels'

                status = 'Present('+json_data[i]['status']+')' #Status

                movegroup = row['Movegroup'] #Movegroup
                treatment = row['Migration Path'] #MigrationPath

                #TargetSize
                if(row['Target Size']==json_machine_type):
                    target_size = 'OK'
                else:
                    target_size = 'NOT OK('+json_machine_type+', should be '+row['Target Size']+')'

                #TargetDiskType
                if(row['Target Disk Type'].lower()==json_disktype): #Checks the disk type of bootdisk only
                    target_disk_type = 'OK'
                else:
                    print(row['Target Disk Type'].lower())
                    target_disk_type = 'NOT OK('+json_disktype+', should be '+row['Target Disk Type'].lower()+')'

                #DiskCount
                if(row['Disk Count']==len(json_data[0]['disks'])):
                    count_of_disks = 'OK'
                else:
                    count_of_disks = 'NOT OK('+str(len(json_data[0]['disks']))+', should be '+str(int(row['Disk Count']))+')'

                #Project
                if(extractor(1, row['Project']) == json_project):
                    project = 'OK'
                else:
                    project = 'NOT OK('+str(json_project)+', should be '+str(extractor(1, row['Project']))+')'

                #Region
                if(row['Region']==json_region):
                    region = 'OK'
                else:
                    region = 'NOT OK('+json_region+', should be '+row['Region']+')'

                #Zone
                if(row['Zone'] == json_zone):
                    zone = 'OK'
                else:
                    zone = 'NOT OK('+json_zone+', should be '+row['Zone']+')'

                #VPC
                if(row['VPC'] == json_vpc):
                    vpc = 'OK'
                else:
                    vpc = 'NOT OK('+json_vpc+', should be '+row['VPC']+')'

                #Subnet
                if(extractor(1, row['Subnet']) == json_subnet):
                    subnet = 'OK'
                else:
                    subnet = 'NOT OK('+json_subnet+', should be '+extractor(1, row['Subnet'])+')'

                #InternalIP

                if(row['Target Internal IP'] == json_data[i]['networkInterfaces'][0]['networkIP']):
                    internal_ip = 'OK'
                else:
                    internal_ip = 'NOT OK('+json_data[i]['networkInterfaces'][0]['networkIP']+', should be '+row['Target Internal IP']+')'

                #ExternalIP
                try:
                    if(row['Need for External IP']=='Yes' and type(json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP']) is str):
                        if(exipopt == 'ephemeral'):
                            external_ip = 'OK'
                        else:
                            if(json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP'] == exipopt):
                                external_ip = 'OK'
                            else:
                                external_ip = 'NOT OK('+json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP']+'should be '+exipopt+')'
                    else:
                        if(row['Need for External IP']=='No' and type(json_data[i]['networkInterfaces'][0]['accessConfigs'][0]['natIP']) is str):
                            external_ip = 'NOT OK (IP on Target Not Required)'
                        
                except KeyError:
                    if(row['Need for External IP']=='Yes'):
                        external_ip = 'NOT OK (Missing on the target)'
                    else:
                        external_ip = 'OK'

                if json_nettags == 'No Net Tag':
                    network_tags = 'NOT OK(Not Present)'
                else:
                    if(json_nettags == nettags):
                        network_tags = 'OK'
                    else:
                        network_tags = 'NOT OK( Tags should be '+str(nettags)+', but is '+str(json_nettags)+')'

                #Service Account
                if(json_data[i]['serviceAccounts'][0]['email'] == row['Service Account']):
                    service_account = 'OK'
                else:
                    service_account = 'NOT OK('+json_data[i]['serviceAccounts'][0]['email']+', should be '+row['Service Account']+')'


                label_lines = row['Labels'].split('\n')

                # Initialize an empty dictionary
                s_labels = {}

                # Iterate over the lines and split each line into key-value pairs
                for line in label_lines:
                    if line:
                        key, value = line.split(':', 1)  # Split each line into key-value pair
                        s_labels[key.strip()] = value.strip()



                if json_labels == 'No Labels':
                    labels = 'NOT OK(No Labels are Present)'
                else:
                    if(json_labels == s_labels):
                        labels = 'OK'
                    else:
                        #dict1-json_labels #dict2-s_labels
                        items_difference = {key: json_labels[key] for key in json_labels if key not in s_labels or json_labels[key] != s_labels[key]}
                        #TRue for present in VMMapping
                        #False for present in VM

                        if(items_difference.items() <= json_labels.items()):
                            labels = 'NOT OK( Additional Labels: '+str(items_difference)+' )'
                        else:
                            labels = 'NOT OK( Missing Labels: '+str(items_difference)+' )'




                print(name)
                print(status)
                print(movegroup)
                print(treatment)
                print(target_size)
                print(target_disk_type)
                print(count_of_disks)
                print(project)
                print(region)
                print(zone)
                print(vpc)
                print(subnet)
                print(internal_ip)
                print(external_ip)
                print(network_tags)
                print(service_account)
                print(labels)

                print('-------------------------------------------------------------')

            else:
                status = 'Not Present' #Status
                movegroup = row['Movegroup'] #Movegroup
                treatment = row['Migration Path'] #MigrationPath
                name = ''
                target_size = ''
                target_disk_type = ''
                count_of_disks = ''
                project = ''
                region = ''
                zone = ''
                vpc = ''
                subnet = ''
                internal_ip = ''
                external_ip = ''
                network_tags = ''
                service_account = ''
                labels = ''


                print(name)
                print(status)
                print(movegroup)
                print(treatment)
                print(target_size)
                print(target_disk_type)
                print(count_of_disks)
                print(project)
                print(region)
                print(zone)
                print(vpc)
                print(subnet)
                print(internal_ip)
                print(external_ip)
                print(network_tags)
                print(service_account)
                print(labels)


            temp = pd.DataFrame({"VM Name":[name],
                                "Status":[status],
                                "Move Group":[movegroup],
                                "Treatment":[treatment],
                                "Target Size":[target_size],
                                "Count of Disks":[count_of_disks],
                                "Target Disk Type":[target_disk_type],
                                "Project":[project],
                                "Region":[region],
                                "Zone":[zone],
                                "VPC":[vpc],
                                "Subnet":[subnet],
                                "Internal IP":[internal_ip],
                                "External IP":[external_ip],
                                "Network Tags":[network_tags],
                                "Service Account":[service_account],
                                "Labels":[labels]})

            data = pd.concat([data, temp])

    data

    data.to_excel(result_xl, index=False)

    #Formatting Part
    wb = openpyxl.load_workbook(result_xl)

    for sheet in wb.worksheets:

        val = sheet.max_column #should be replaced with max_col

        #Content for Heading Colour Filter
        for row in sheet.iter_rows(min_row=1, max_col=val, max_row=1):
            for c in row:
                c.fill = PatternFill('solid', fgColor = 'a9a9a9')

        #Dynamically edits the column width according to the content pasted there.
        dims = {}
        for row in sheet.rows:
            for cell in row:
                if cell.value:
                    dims[cell.column_letter] = max((dims.get(cell.column_letter, 0), len(str(cell.value))))
        for col, value in dims.items():
            sheet.column_dimensions[col].width = value+2

    # Set the column width as +2 greater than the first row's width
        for col, width in dims.items():
            sheet.column_dimensions[col].width = width + 6

        for row in sheet.rows:
                for cell in row:
                    if cell.value == 'OK':
                        cell.fill = PatternFill(start_color='5CFF5C', end_color='5CFF5C', fill_type='solid')  # Green
                    elif 'NOT OK' in str(cell.value):
                        cell.fill = PatternFill(start_color='FF3D33', end_color='FF3D33', fill_type='solid')  # Red

                    if 'Present(' in str(cell.value):
                        cell.fill = PatternFill(start_color='5CFF5C', end_color='5CFF5C', fill_type='solid')  # Green
                    elif 'Not Present' in str(cell.value):
                        cell.fill = PatternFill(start_color='FF3D33', end_color='FF3D33', fill_type='solid')  # Green

        #Autofilter for all headings
        sheet.auto_filter.ref = sheet.dimensions


        #Remove ; and replace with new line
        for row in sheet.iter_rows():
            for cell in row:
                # Check if the cell value is a string
                if isinstance(cell.value, str):
                    # Use re.sub to replace the text in the cell
                    cell.value = re.sub(r' ; ', chr(13)+chr(10), cell.value)

    wb.save(result_xl)

    return result_xl, ''

def aws_mov_validate(mgg, ec2_data, path, dir, arn):
    """
    AWS-specific movegroup validation function
    Validates AWS VM mapping sheet against EC2 inventory data
    """
    xls = pd.ExcelFile(path)
    oname = 'validate_'+mgg+'_'+arn.split(':')[4]+'.xlsx'
    result_xl = os.path.join(dir, oname)

    # Create instances list from EC2 data
    instances_list = []
    ec2_instances = ec2_data.get('EC2', [])

    # Skip header row and extract instance names
    for instance_row in ec2_instances[1:]:  # Skip header
        if len(instance_row) > 1:  # Ensure row has data
            instance_name = instance_row[1]  # Name is at index 1
            if instance_name:
                instances_list.append(instance_name.lower())

    print("AWS Instances found:", instances_list)

    # Create DataFrame with AWS-specific columns
    mg = mgg
    data = pd.DataFrame({
        "VM Name": [],
        "Status": [],
        "Move Group": [],
        "Treatment": [],
        "Target Size": [],
        "Count of Disks": [],
        "Target Disk Type": [],
        "Account": [],
        "Region": [],
        "Availability Zone": [],
        "VPC": [],
        "Subnet": [],
        "Internal IP": [],
        "External IP": [],
        "Security Groups": [],
        "Platform": [],
        "Tags": []
    })

    sheets = xls.sheet_names
    df = pd.read_excel(xls, sheets[0])  # Reading the VMs sheet in VMMapping
    df.fillna("", inplace=True)

    for index, row in df.iterrows():
        try:
            print(row['Movegroup'])
        except:
            return '', "Please check whether you have uploaded the proper excel file."

        if row['Movegroup'] == mg:
            MapName = row['Instance Name']

            if MapName.lower() in instances_list:
                # Find matching EC2 instance data
                matching_instance = None
                for instance_row in ec2_instances[1:]:  # Skip header
                    if len(instance_row) > 1 and instance_row[1] and instance_row[1].lower() == MapName.lower():
                        matching_instance = instance_row
                        break

                if matching_instance:
                    name = row['Name']  # VM Name
                    status = f'Present({matching_instance[14]})'  # Status from EC2 state
                    movegroup = row['Movegroup']
                    treatment = row['Migration Path']

                    # Target Size validation
                    current_instance_type = matching_instance[3]  # Instance Type
                    if row['Target Size'] == current_instance_type:
                        target_size = 'OK'
                    else:
                        target_size = f'NOT OK({current_instance_type}, should be {row["Target Size"]})'

                    # Count of Disks - extract from volumes info
                    volumes_info = matching_instance[16] if len(matching_instance) > 16 else ""
                    disk_count = len(volumes_info.split(',')) if volumes_info else 1
                    count_of_disks = str(disk_count)

                    # Target Disk Type validation
                    current_disk_type = matching_instance[20] if len(matching_instance) > 20 else ""
                    if row['Target Disk Type'].lower() == current_disk_type.lower():
                        target_disk_type = 'OK'
                    else:
                        target_disk_type = f'NOT OK({current_disk_type}, should be {row["Target Disk Type"]})'

                    account = arn.split(':')[4]  # Extract account ID from ARN
                    region = matching_instance[0]  # Region
                    zone = matching_instance[6]  # Availability Zone
                    vpc = matching_instance[12]  # VPC ID
                    subnet = matching_instance[11]  # Subnet ID

                    # Internal IP validation
                    current_internal_ip = matching_instance[7]  # Private IP
                    if row['Target Internal IP'] == current_internal_ip:
                        internal_ip = 'OK'
                    else:
                        internal_ip = f'NOT OK({current_internal_ip}, should be {row["Target Internal IP"]})'

                    # External IP validation
                    current_external_ip = matching_instance[8]  # Public IP
                    if row['Need for External IP'] == 'Yes':
                        if current_external_ip:
                            if row['External IP'] == "" or row['External IP'] == 'ephemeral':
                                external_ip = 'OK'
                            elif row['External IP'] == current_external_ip:
                                external_ip = 'OK'
                            else:
                                external_ip = f'NOT OK({current_external_ip}, should be {row["External IP"]})'
                        else:
                            external_ip = 'NOT OK (Missing on the target)'
                    else:
                        if current_external_ip:
                            external_ip = 'NOT OK (IP on Target Not Required)'
                        else:
                            external_ip = 'OK'

                    # Security Groups
                    security_groups = matching_instance[15] if len(matching_instance) > 15 else ""

                    # Platform
                    platform = matching_instance[13] if len(matching_instance) > 13 else ""

                    # Tags validation
                    current_tags = matching_instance[23] if len(matching_instance) > 23 else ""
                    expected_tags = row['Tags'] if 'Tags' in row else ""

                    if expected_tags:
                        if current_tags and expected_tags.lower() in current_tags.lower():
                            tags = 'OK'
                        else:
                            tags = f'NOT OK(Expected: {expected_tags}, Current: {current_tags})'
                    else:
                        tags = 'OK' if not current_tags else f'Additional tags present: {current_tags}'

                else:
                    # Instance found in list but detailed data not available
                    name = row['Name']
                    status = 'Present(Data Incomplete)'
                    movegroup = row['Movegroup']
                    treatment = row['Migration Path']
                    target_size = 'Unable to validate'
                    count_of_disks = 'Unable to validate'
                    target_disk_type = 'Unable to validate'
                    account = arn.split(':')[4]
                    region = 'Unable to validate'
                    zone = 'Unable to validate'
                    vpc = 'Unable to validate'
                    subnet = 'Unable to validate'
                    internal_ip = 'Unable to validate'
                    external_ip = 'Unable to validate'
                    security_groups = 'Unable to validate'
                    platform = 'Unable to validate'
                    tags = 'Unable to validate'

            else:
                # Instance not found
                name = row['Name']
                status = 'Not Present'
                movegroup = row['Movegroup']
                treatment = row['Migration Path']
                target_size = ''
                count_of_disks = ''
                target_disk_type = ''
                account = arn.split(':')[4]
                region = ''
                zone = ''
                vpc = ''
                subnet = ''
                internal_ip = ''
                external_ip = ''
                security_groups = ''
                platform = ''
                tags = ''

            # Add row to DataFrame
            temp = pd.DataFrame({
                "VM Name": [name],
                "Status": [status],
                "Move Group": [movegroup],
                "Treatment": [treatment],
                "Target Size": [target_size],
                "Count of Disks": [count_of_disks],
                "Target Disk Type": [target_disk_type],
                "Account": [account],
                "Region": [region],
                "Availability Zone": [zone],
                "VPC": [vpc],
                "Subnet": [subnet],
                "Internal IP": [internal_ip],
                "External IP": [external_ip],
                "Security Groups": [security_groups],
                "Platform": [platform],
                "Tags": [tags]
            })

            data = pd.concat([data, temp])

    # Save to Excel with formatting (same as GCP version)
    data.to_excel(result_xl, index=False)

    # Apply formatting
    wb = openpyxl.load_workbook(result_xl)
    for sheet in wb.worksheets:
        val = sheet.max_column

        # Header formatting
        for row in sheet.iter_rows(min_row=1, max_col=val, max_row=1):
            for c in row:
                c.fill = PatternFill('solid', fgColor='a9a9a9')

        # Auto-adjust column widths
        dims = {}
        for row in sheet.rows:
            for cell in row:
                if cell.value:
                    dims[cell.column_letter] = max((dims.get(cell.column_letter, 0), len(str(cell.value))))
        for col, value in dims.items():
            sheet.column_dimensions[col].width = value + 6

        # Color coding for validation results
        for row in sheet.rows:
            for cell in row:
                if cell.value == 'OK':
                    cell.fill = PatternFill(start_color='5CFF5C', end_color='5CFF5C', fill_type='solid')  # Green
                elif 'NOT OK' in str(cell.value):
                    cell.fill = PatternFill(start_color='FF3D33', end_color='FF3D33', fill_type='solid')  # Red
                if 'Present(' in str(cell.value):
                    cell.fill = PatternFill(start_color='5CFF5C', end_color='5CFF5C', fill_type='solid')  # Green
                elif 'Not Present' in str(cell.value):
                    cell.fill = PatternFill(start_color='FF3D33', end_color='FF3D33', fill_type='solid')  # Red

        # Auto filter
        sheet.auto_filter.ref = sheet.dimensions

        # Replace semicolons with newlines
        for row in sheet.iter_rows():
            for cell in row:
                if isinstance(cell.value, str):
                    cell.value = re.sub(r' ; ', chr(13)+chr(10), cell.value)

    wb.save(result_xl)
    return result_xl, ''

def move_vali(request, rootpath):
    """
    Main movegroup validation function that supports both GCP and AWS providers
    """
    dir1 = os.path.join(rootpath, 'migration', 'move_val')

    # Get data from form and save it
    mg = request.form['move-group']
    provider = request.form.get('provider', 'gcp')  # Default to GCP for backward compatibility
    file = request.files['excel-file']
    file.save(os.path.join(dir1, file.filename))
    excel_path = os.path.join(dir1, file.filename)

    if provider.lower() == 'aws':
        # AWS validation path
        arn = request.form['arn']
        regions = request.form.get('regions', '*')  # Default to all regions

        try:
            # Get AWS credentials using the ARN
            creds = assume_aws_role_with_google_id_token(arn)

            # Get regions list
            if regions == '*':
                # For now, use common regions. In production, you might want to get all regions
                region_list = ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1']
            else:
                region_list = [r.strip() for r in regions.split(',')]

            # Get EC2 inventory data
            ec2_data = ec2(region_list, creds)

            # Call AWS validation function
            file_path, error = aws_mov_validate(mg, ec2_data, excel_path, dir1, arn)

        except Exception as e:
            err = f"AWS authentication or data retrieval failed: {str(e)}"
            return '', err, "Check AWS ARN and permissions", 1

    else:
        # GCP validation path (existing logic)
        pj = request.form['projid']

        config = subprocess.run(['/usr/bin/pwsh','-c','gcloud', 'config', 'set', 'project', pj],
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

        config_out = config.stderr
        print(config_out)

        if 'WARNING:' in config_out:
            lines = config_out.strip().split('.')
            msg = 'Check whether access is given to the mentioned service account.'
            err = lines[0]+'\n or Check whether access is given to the mentioned service account'
            return '', err, msg, 1

        gcloud_int = 'gcloud compute instances list --format=json'
        instances = subprocess.run(['/usr/bin/pwsh','-c', gcloud_int],
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

        data = instances.stdout

        # Call GCP validation function
        file_path, error = mov_validate(mg, data, excel_path, dir1, pj)

    # Common response handling
    if os.path.isfile(file_path):
        msg = "Validation file created and downloaded successfully."
        return file_path, "", msg, 0
    else:
        err1 = "Internal Error"
        return "", err1, error, 1